import { Building2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>3 } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { BRANDS } from "@/constants/brands";

const Index = () => {
  const totalPendingReplies = BRANDS.reduce((sum, brand) => sum + brand.pendingReplies, 0);
  const activeBrands = BRANDS.filter(brand => brand.status === 'active').length;

  return (
    <div className="flex-1 bg-gradient-subtle min-h-screen">
      <div className="container mx-auto px-6 py-8 space-y-8">
        {/* Welcome Section */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-foreground">Brand Response AI</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Manage responses across all your brands with AI-powered assistance
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl mx-auto">
          <Card className="text-center">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Active Brands
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{activeBrands}</div>
              <p className="text-xs text-muted-foreground">of {BRANDS.length} total</p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Pending Replies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{totalPendingReplies}</div>
              <p className="text-xs text-muted-foreground">across all brands</p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                AI Assistance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-accent">Active</div>
              <p className="text-xs text-muted-foreground">ready to help</p>
            </CardContent>
          </Card>
        </div>

        {/* Brand Cards */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold text-foreground text-center">Your Brands</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl mx-auto">
            {BRANDS.map((brand) => (
              <Card key={brand.id} className="transition-all duration-200 hover:shadow-md hover:scale-[1.02]">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Building2 className="w-5 h-5 text-primary" />
                      <CardTitle className="text-lg">{brand.name}</CardTitle>
                    </div>
                    <Badge className={brand.status === 'active' ? 'bg-accent text-accent-foreground' : 'bg-muted text-muted-foreground'}>
                      {brand.status}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Pending Replies</span>
                    <span className={`font-bold ${brand.pendingReplies > 10 ? 'text-destructive' : brand.pendingReplies > 5 ? 'text-warning' : 'text-accent'}`}>
                      {brand.pendingReplies}
                    </span>
                  </div>

                  <div className="flex gap-2">
                    <Button asChild size="sm" variant="outline" className="flex-1">
                      <Link to={`/brand/${brand.id}/analytics`}>
                        <BarChart3 className="w-4 h-4 mr-1" />
                        Analytics
                      </Link>
                    </Button>
                    <Button asChild size="sm" variant={brand.pendingReplies > 0 ? "default" : "outline"} className="flex-1">
                      <Link to={`/brand/${brand.id}/messages`}>
                        <MessageSquare className="w-4 h-4 mr-1" />
                        Messages
                        {brand.pendingReplies > 0 && (
                          <Badge className="ml-1 h-4 px-1 text-xs bg-background text-foreground">
                            {brand.pendingReplies}
                          </Badge>
                        )}
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Getting Started */}
        <div className="text-center space-y-4 max-w-2xl mx-auto">
          <h3 className="text-lg font-semibold text-foreground">Getting Started</h3>
          <p className="text-muted-foreground">
            Select a brand from above to view analytics or manage messages.
            Use the sidebar to quickly navigate between brands and features.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Index;