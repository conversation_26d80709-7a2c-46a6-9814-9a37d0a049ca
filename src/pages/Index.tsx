import { useState, useEffect } from "react";
import {
  TrendingUp,
  MessageSquare,
  Building2,
  Users,
  BarChart3,
  Clock,
  Activity,
  Zap,
  RefreshCw
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { DashboardSkeleton } from "@/components/ui/dashboard-skeleton";
import {
  StatsCard,
  BrandOverviewCard,
  RecentActivityFeed
} from "@/components/features/dashboard";
import { DashboardService } from "@/services";
import { DashboardStats, BrandOverview, RecentActivity } from "@/types/dashboard";
import { useNotifications } from "@/hooks/use-notifications";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

const Index = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [brandOverviews, setBrandOverviews] = useState<BrandOverview[]>([]);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);

  const { showToast } = useNotifications();
  const isMobile = useIsMobile();

  const loadDashboardData = async (showRefreshFeedback = false) => {
    if (showRefreshFeedback) {
      setIsRefreshing(true);
    }

    try {
      const data = await DashboardService.fetchDashboardData();
      setStats(data.stats);
      setBrandOverviews(data.brandOverviews);
      setRecentActivity(data.recentActivity);

      if (showRefreshFeedback) {
        showToast("success", "Dashboard refreshed successfully");
      }
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
      showToast("error", "Failed to load dashboard data");
    } finally {
      setIsLoading(false);
      if (showRefreshFeedback) {
        setIsRefreshing(false);
      }
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex-1 bg-gradient-subtle min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <DashboardSkeleton />
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-gradient-subtle min-h-screen">
      <div className="container mx-auto px-6 py-8 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h1 className="text-3xl font-bold text-foreground">
              Brand Response Dashboard
            </h1>
            <p className="text-muted-foreground">
              Monitor and manage all your brand responses in one place
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadDashboardData(true)}
            disabled={isRefreshing}
            className={cn(isRefreshing && "opacity-50")}
          >
            <RefreshCw className={cn("w-4 h-4", isRefreshing && "animate-spin")} />
            {!isMobile && <span className="ml-2">
              {isRefreshing ? "Refreshing..." : "Refresh"}
            </span>}
          </Button>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <StatsCard
              title="Total Brands"
              value={stats.totalBrands}
              description={`${stats.activeBrands} active`}
              icon={<Building2 className="w-4 h-4" />}
              variant="primary"
              trend={{
                value: "+2 this month",
                direction: "up"
              }}
            />
            <StatsCard
              title="Pending Replies"
              value={stats.totalPendingReplies}
              description="Across all brands"
              icon={<MessageSquare className="w-4 h-4" />}
              variant={stats.totalPendingReplies > 20 ? "warning" : "success"}
              trend={{
                value: "-12% vs last week",
                direction: "down"
              }}
            />
            <StatsCard
              title="Response Rate"
              value={`${stats.responseRate}%`}
              description="Last 30 days"
              icon={<TrendingUp className="w-4 h-4" />}
              variant="success"
              trend={{
                value: "+5% vs last month",
                direction: "up"
              }}
            />
            <StatsCard
              title="Avg Response Time"
              value={stats.avgResponseTime}
              description="AI + Human combined"
              icon={<Clock className="w-4 h-4" />}
              variant="default"
              trend={{
                value: "-30min vs last week",
                direction: "down"
              }}
            />
          </div>
        )}

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Brand Overview Cards */}
          <div className="lg:col-span-2 space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-foreground">Brand Overview</h2>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Activity className="w-4 h-4" />
                <span>Live updates</span>
              </div>
            </div>

            {brandOverviews.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                <Building2 className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">No brands configured</p>
                <p className="text-sm">Add your first brand to get started</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {brandOverviews.map((brand) => (
                  <BrandOverviewCard key={brand.id} brand={brand} />
                ))}
              </div>
            )}
          </div>

          {/* Recent Activity Feed */}
          <div className="space-y-4">
            <RecentActivityFeed activities={recentActivity} />

            {/* Quick Actions */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground">Quick Actions</h3>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start" asChild>
                  <a href="#" onClick={(e) => e.preventDefault()}>
                    <Zap className="w-4 h-4 mr-2" />
                    Generate AI Responses
                  </a>
                </Button>
                <Button variant="outline" className="w-full justify-start" asChild>
                  <a href="#" onClick={(e) => e.preventDefault()}>
                    <BarChart3 className="w-4 h-4 mr-2" />
                    View Analytics Report
                  </a>
                </Button>
                <Button variant="outline" className="w-full justify-start" asChild>
                  <a href="#" onClick={(e) => e.preventDefault()}>
                    <Users className="w-4 h-4 mr-2" />
                    Manage Team Access
                  </a>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;