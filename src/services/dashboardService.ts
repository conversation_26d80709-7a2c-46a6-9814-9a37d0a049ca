import { DashboardStats, RecentActivity, BrandOverview } from "@/types/dashboard";
import { BRANDS } from "@/constants/brands";
import { MOCK_POSTS_DATA } from "@/constants/mockData";

export class DashboardService {
  static getDashboardStats(): DashboardStats {
    const totalBrands = BRANDS.length;
    const activeBrands = BRANDS.filter(brand => brand.status === 'active').length;
    const totalPendingReplies = BRANDS.reduce((sum, brand) => sum + brand.pendingReplies, 0);
    
    // Calculate total posts across all brands
    const totalPosts = Object.values(MOCK_POSTS_DATA).reduce((sum, posts) => sum + posts.length, 0);
    
    // Mock response rate and avg response time
    const responseRate = 87; // 87%
    const avgResponseTime = "2.3h";

    return {
      totalBrands,
      activeBrands,
      totalPendingReplies,
      totalPosts,
      responseRate,
      avgResponseTime
    };
  }

  static getBrandOverviews(): BrandOverview[] {
    return BRANDS.map(brand => {
      const posts = MOCK_POSTS_DATA[brand.id] || [];
      const totalPosts = posts.length;
      
      // Calculate response rate (mock data)
      const responseRate = Math.floor(Math.random() * 30) + 70; // 70-100%
      
      // Get unique platforms
      const platforms = [...new Set(posts.map(post => post.platform))];
      
      // Mock last activity
      const lastActivity = this.getRandomLastActivity();
      
      // Mock trend
      const trends: ('up' | 'down' | 'stable')[] = ['up', 'down', 'stable'];
      const trend = trends[Math.floor(Math.random() * trends.length)];

      return {
        id: brand.id,
        name: brand.name,
        status: brand.status,
        pendingReplies: brand.pendingReplies,
        totalPosts,
        responseRate,
        lastActivity,
        platforms,
        trend
      };
    });
  }

  static getRecentActivity(): RecentActivity[] {
    const activities: RecentActivity[] = [
      {
        id: "1",
        type: "comment",
        brandId: "techcorp",
        brandName: "TechCorp",
        message: "New comment on 'Product Launch' post requires attention",
        timestamp: "5 minutes ago",
        priority: "high",
        platform: "LinkedIn"
      },
      {
        id: "2",
        type: "reply",
        brandId: "innovate-labs",
        brandName: "Innovate Labs",
        message: "AI reply sent successfully to customer inquiry",
        timestamp: "12 minutes ago",
        priority: "medium",
        platform: "Twitter"
      },
      {
        id: "3",
        type: "mention",
        brandId: "future-systems",
        brandName: "Future Systems",
        message: "Brand mentioned in industry discussion",
        timestamp: "1 hour ago",
        priority: "medium",
        platform: "LinkedIn"
      },
      {
        id: "4",
        type: "system",
        brandId: "",
        brandName: "",
        message: "AI model updated with latest training data",
        timestamp: "2 hours ago",
        priority: "low"
      },
      {
        id: "5",
        type: "comment",
        brandId: "creative-agency",
        brandName: "Creative Agency",
        message: "Multiple comments on portfolio showcase post",
        timestamp: "3 hours ago",
        priority: "medium",
        platform: "Instagram"
      }
    ];

    return activities;
  }

  private static getRandomLastActivity(): string {
    const activities = [
      "2 minutes ago",
      "15 minutes ago",
      "1 hour ago",
      "3 hours ago",
      "1 day ago",
      "2 days ago"
    ];
    return activities[Math.floor(Math.random() * activities.length)];
  }

  static async fetchDashboardData(): Promise<{
    stats: DashboardStats;
    brandOverviews: BrandOverview[];
    recentActivity: RecentActivity[];
  }> {
    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          stats: this.getDashboardStats(),
          brandOverviews: this.getBrandOverviews(),
          recentActivity: this.getRecentActivity()
        });
      }, 800);
    });
  }
}
