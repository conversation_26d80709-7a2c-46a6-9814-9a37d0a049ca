// Dashboard specific types
export interface DashboardStats {
  totalBrands: number;
  totalPendingReplies: number;
  responseRate: number;
  avgResponseTime: string;
  activeBrands: number;
  totalPosts: number;
}

export interface RecentActivity {
  id: string;
  type: 'comment' | 'reply' | 'mention' | 'system';
  brandId: string;
  brandName: string;
  message: string;
  timestamp: string;
  priority: 'low' | 'medium' | 'high';
  platform?: string;
}

export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  variant: 'primary' | 'secondary' | 'outline';
}

export interface BrandOverview {
  id: string;
  name: string;
  status: 'active' | 'paused';
  pendingReplies: number;
  totalPosts: number;
  responseRate: number;
  lastActivity: string;
  platforms: string[];
  trend: 'up' | 'down' | 'stable';
}
