import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ReactNode;
  trend?: {
    value: string;
    direction: 'up' | 'down' | 'stable';
  };
  variant?: 'default' | 'primary' | 'success' | 'warning';
  className?: string;
}

export function StatsCard({ 
  title, 
  value, 
  description, 
  icon, 
  trend, 
  variant = 'default',
  className 
}: StatsCardProps) {
  const variantStyles = {
    default: "border-border",
    primary: "border-primary/20 bg-primary-light/50",
    success: "border-accent/20 bg-accent-light/50",
    warning: "border-warning/20 bg-warning-light/50"
  };

  const getTrendColor = (direction: 'up' | 'down' | 'stable') => {
    switch (direction) {
      case 'up':
        return 'text-accent';
      case 'down':
        return 'text-destructive';
      case 'stable':
        return 'text-muted-foreground';
    }
  };

  const getTrendIcon = (direction: 'up' | 'down' | 'stable') => {
    switch (direction) {
      case 'up':
        return '↗';
      case 'down':
        return '↘';
      case 'stable':
        return '→';
    }
  };

  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-md hover:scale-[1.02]",
      variantStyles[variant],
      className
    )}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <div className="text-2xl font-bold text-foreground">
              {value}
            </div>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">
                {description}
              </p>
            )}
          </div>
          {trend && (
            <Badge 
              variant="outline" 
              className={cn(
                "text-xs font-medium",
                getTrendColor(trend.direction)
              )}
            >
              <span className="mr-1">
                {getTrendIcon(trend.direction)}
              </span>
              {trend.value}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
