import { Link } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Building2, MessageSquare, BarChart3, Clock, TrendingUp, TrendingDown, Minus } from "lucide-react";
import { BrandOverview } from "@/types/dashboard";
import { cn } from "@/lib/utils";

interface BrandOverviewCardProps {
  brand: BrandOverview;
}

export function BrandOverviewCard({ brand }: BrandOverviewCardProps) {
  const getTrendIcon = () => {
    switch (brand.trend) {
      case 'up':
        return <TrendingUp className="w-3 h-3 text-accent" />;
      case 'down':
        return <TrendingDown className="w-3 h-3 text-destructive" />;
      case 'stable':
        return <Minus className="w-3 h-3 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: string) => {
    return status === 'active' ? 'bg-accent text-accent-foreground' : 'bg-muted text-muted-foreground';
  };

  const getPriorityColor = (pendingReplies: number) => {
    if (pendingReplies > 10) return 'text-destructive';
    if (pendingReplies > 5) return 'text-warning';
    return 'text-accent';
  };

  return (
    <Card className="transition-all duration-200 hover:shadow-md hover:scale-[1.02] group">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-primary" />
            <CardTitle className="text-lg font-semibold">{brand.name}</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            {getTrendIcon()}
            <Badge className={getStatusColor(brand.status)}>
              {brand.status}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Pending Replies</p>
            <p className={cn("text-xl font-bold", getPriorityColor(brand.pendingReplies))}>
              {brand.pendingReplies}
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Response Rate</p>
            <p className="text-xl font-bold text-foreground">{brand.responseRate}%</p>
          </div>
        </div>

        {/* Additional Info */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Total Posts</span>
            <span className="font-medium">{brand.totalPosts}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Last Activity</span>
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3 text-muted-foreground" />
              <span className="font-medium">{brand.lastActivity}</span>
            </div>
          </div>
        </div>

        {/* Platforms */}
        {brand.platforms.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">Platforms</p>
            <div className="flex flex-wrap gap-1">
              {brand.platforms.map((platform) => (
                <Badge key={platform} variant="outline" className="text-xs">
                  {platform}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button 
            asChild 
            size="sm" 
            variant="outline" 
            className="flex-1"
          >
            <Link to={`/brand/${brand.id}/analytics`}>
              <BarChart3 className="w-4 h-4 mr-1" />
              Analytics
            </Link>
          </Button>
          <Button 
            asChild 
            size="sm" 
            variant={brand.pendingReplies > 0 ? "default" : "outline"}
            className="flex-1"
          >
            <Link to={`/brand/${brand.id}/messages`}>
              <MessageSquare className="w-4 h-4 mr-1" />
              Messages
              {brand.pendingReplies > 0 && (
                <Badge className="ml-1 h-4 px-1 text-xs bg-background text-foreground">
                  {brand.pendingReplies}
                </Badge>
              )}
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
