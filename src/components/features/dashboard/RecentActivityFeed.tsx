import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  MessageSquare, 
  Reply, 
  AtSign, 
  Settings, 
  Clock,
  ExternalLink
} from "lucide-react";
import { RecentActivity } from "@/types/dashboard";
import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";

interface RecentActivityFeedProps {
  activities: RecentActivity[];
  maxItems?: number;
}

export function RecentActivityFeed({ activities, maxItems = 5 }: RecentActivityFeedProps) {
  const displayedActivities = activities.slice(0, maxItems);

  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'comment':
        return <MessageSquare className="w-4 h-4" />;
      case 'reply':
        return <Reply className="w-4 h-4" />;
      case 'mention':
        return <AtSign className="w-4 h-4" />;
      case 'system':
        return <Settings className="w-4 h-4" />;
    }
  };

  const getActivityColor = (type: RecentActivity['type']) => {
    switch (type) {
      case 'comment':
        return 'text-primary bg-primary-light';
      case 'reply':
        return 'text-accent bg-accent-light';
      case 'mention':
        return 'text-warning bg-warning-light';
      case 'system':
        return 'text-muted-foreground bg-muted';
    }
  };

  const getPriorityColor = (priority: RecentActivity['priority']) => {
    switch (priority) {
      case 'high':
        return 'border-l-destructive bg-destructive/5';
      case 'medium':
        return 'border-l-warning bg-warning/5';
      case 'low':
        return 'border-l-muted bg-muted/5';
    }
  };

  const getPriorityBadge = (priority: RecentActivity['priority']) => {
    const variants = {
      high: 'destructive',
      medium: 'secondary',
      low: 'outline'
    } as const;

    return (
      <Badge variant={variants[priority]} className="text-xs">
        {priority}
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Recent Activity</CardTitle>
          <Button variant="ghost" size="sm" asChild>
            <Link to="/activity">
              View All
              <ExternalLink className="w-3 h-3 ml-1" />
            </Link>
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {displayedActivities.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No recent activity</p>
          </div>
        ) : (
          displayedActivities.map((activity) => (
            <div
              key={activity.id}
              className={cn(
                "flex items-start gap-3 p-3 rounded-lg border-l-2 transition-colors hover:bg-muted/50",
                getPriorityColor(activity.priority)
              )}
            >
              <div className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full",
                getActivityColor(activity.type)
              )}>
                {getActivityIcon(activity.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-foreground leading-tight">
                      {activity.message}
                    </p>
                    {activity.brandName && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {activity.brandName}
                        {activity.platform && (
                          <span className="ml-1">• {activity.platform}</span>
                        )}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-2 flex-shrink-0">
                    {getPriorityBadge(activity.priority)}
                  </div>
                </div>
                
                <div className="flex items-center gap-1 mt-2">
                  <Clock className="w-3 h-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">
                    {activity.timestamp}
                  </span>
                </div>
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
}
