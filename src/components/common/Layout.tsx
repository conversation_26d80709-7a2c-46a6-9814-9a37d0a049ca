import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/features/navigation";
import { LayoutProps } from "@/types";
import { useIsMobile } from "@/hooks/use-mobile";

export function Layout({ children }: LayoutProps) {
  const isMobile = useIsMobile();

  return (
    <SidebarProvider defaultOpen={!isMobile}>
      {/* Skip to main content link for keyboard users */}
      <a
        href="#main-content"
        className="skip-link"
        onFocus={(e) => e.target.scrollIntoView()}
      >
        Skip to main content
      </a>

      <div className="min-h-screen flex w-full">
        <AppSidebar />

        <div className="flex-1 flex flex-col">
          <main id="main-content" className="flex-1" tabIndex={-1}>
            {children}
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
}
